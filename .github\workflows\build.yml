name: Build Site

on:
  issues:
    types:
      - opened
  schedule:
    # 每天 UTC 时间 02:00 (北京时间 10:00) 检查更新
    - cron: '0 2 * * *'
  workflow_dispatch:
    # 允许手动触发
    inputs:
      force_build:
        description: '强制构建 (忽略版本检查)'
        required: false
        default: false
        type: boolean

permissions:
  contents: write
  issues: write

jobs:
  check-updates:
    name: Check for Feishin Updates
    runs-on: ubuntu-latest
    outputs:
      should_build: ${{ steps.version_check.outputs.should_build }}
      latest_version: ${{ steps.version_check.outputs.latest_version }}
      current_version: ${{ steps.version_check.outputs.current_version }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check Feishin Version
        id: version_check
        run: |
          # 获取 Feishin 最新版本
          LATEST_VERSION=$(curl -s https://api.github.com/repos/jeffvli/feishin/releases/latest | jq -r '.tag_name')
          echo "Latest Feishin version: $LATEST_VERSION"

          # 检查当前版本文件是否存在
          if [ -f "FEISHIN_VERSION" ]; then
            CURRENT_VERSION=$(cat FEISHIN_VERSION)
            echo "Current version: $CURRENT_VERSION"
          else
            CURRENT_VERSION="none"
            echo "No current version found"
          fi

          # 设置输出变量
          echo "latest_version=$LATEST_VERSION" >> $GITHUB_OUTPUT
          echo "current_version=$CURRENT_VERSION" >> $GITHUB_OUTPUT

          # 判断是否需要构建
          if [ "${{ github.event.inputs.force_build }}" = "true" ] || [ "$LATEST_VERSION" != "$CURRENT_VERSION" ] || [ "${{ github.event_name }}" = "issues" ]; then
            echo "should_build=true" >> $GITHUB_OUTPUT
            echo "需要构建新版本"
          else
            echo "should_build=false" >> $GITHUB_OUTPUT
            echo "版本无变化，跳过构建"
          fi

  build:
    name: Build and Deploy
    runs-on: ubuntu-latest
    needs: check-updates
    if: needs.check-updates.outputs.should_build == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.PAT }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Build Feishin from Source
        run: |
          echo "正在从源码构建 Feishin..."

          LATEST_VERSION="${{ needs.check-updates.outputs.latest_version }}"
          echo "目标版本: $LATEST_VERSION"

          # 克隆仓库
          echo "克隆 Feishin 仓库..."
          git clone --depth=1 https://github.com/jeffvli/feishin.git
          cd feishin

          echo "当前版本: $(git describe --tags --abbrev=0 2>/dev/null || echo 'unknown')"

          # 检查 package.json
          echo "检查 package.json..."
          if [ -f "package.json" ]; then
            echo "✅ package.json 存在"
            echo "可用脚本:"
            cat package.json | grep -A 20 '"scripts"' || echo "无法解析脚本"
          else
            echo "❌ package.json 不存在"
            exit 1
          fi

          # 安装依赖
          echo "安装依赖..."
          export NODE_OPTIONS="--max-old-space-size=4096"
          export CI=false

          # 使用 pnpm（根据 v0.14.0 更新日志，项目已迁移到 pnpm）
          if command -v pnpm >/dev/null 2>&1; then
            echo "使用 pnpm 安装依赖..."
            pnpm install
          else
            echo "pnpm 不可用，安装 pnpm..."
            npm install -g pnpm
            pnpm install
          fi

          # 尝试构建 Web 版本
          echo "开始构建..."
          BUILD_SUCCESS=false

          # 检查可用的构建脚本
          if pnpm run | grep -q "build:web"; then
            echo "使用 build:web 脚本..."
            if pnpm run build:web; then
              BUILD_SUCCESS=true
              echo "✅ build:web 成功"
            fi
          elif pnpm run | grep -q "build-web"; then
            echo "使用 build-web 脚本..."
            if pnpm run build-web; then
              BUILD_SUCCESS=true
              echo "✅ build-web 成功"
            fi
          elif pnpm run | grep -q "build"; then
            echo "使用通用 build 脚本..."
            if pnpm run build; then
              BUILD_SUCCESS=true
              echo "✅ build 成功"
            fi
          fi

          # 如果 pnpm 构建失败，尝试 npm
          if [ "$BUILD_SUCCESS" = false ]; then
            echo "pnpm 构建失败，尝试 npm..."
            npm install --legacy-peer-deps --force

            if npm run build:web 2>/dev/null; then
              BUILD_SUCCESS=true
              echo "✅ npm build:web 成功"
            elif npm run build 2>/dev/null; then
              BUILD_SUCCESS=true
              echo "✅ npm build 成功"
            elif npx vite build --mode production 2>/dev/null; then
              BUILD_SUCCESS=true
              echo "✅ vite build 成功"
            fi
          fi

          # 如果所有构建都失败，创建一个简单的静态页面
          if [ "$BUILD_SUCCESS" = false ]; then
            echo "❌ 所有构建方法都失败了，创建临时页面..."

            mkdir -p dist
            cat > dist/index.html << EOF
          <!DOCTYPE html>
          <html>
          <head>
              <title>Feishin - 更新中</title>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <style>
                  body {
                      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                      text-align: center;
                      padding: 50px 20px;
                      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                      color: white;
                      min-height: 100vh;
                      margin: 0;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                  }
                  .container {
                      max-width: 600px;
                      background: rgba(255,255,255,0.1);
                      padding: 40px;
                      border-radius: 20px;
                      backdrop-filter: blur(10px);
                  }
                  .error { color: #ffcccb; }
                  .info { color: #add8e6; }
                  a { color: #add8e6; }
                  h1 { font-size: 2.5em; margin-bottom: 20px; }
              </style>
          </head>
          <body>
              <div class="container">
                  <h1>🎵 Feishin</h1>
                  <p class="info">正在更新到最新版本...</p>
                  <p>目标版本: $LATEST_VERSION</p>
                  <p class="error">构建过程遇到问题，请稍后再试。</p>
                  <p><a href="https://github.com/jeffvli/feishin" target="_blank">访问 Feishin 官方仓库</a></p>
                  <p><small>此页面将在构建问题解决后自动更新</small></p>
              </div>
          </body>
          </html>
          EOF
            echo "✅ 创建了临时页面"
          fi

          # 显示构建结果
          echo "构建完成，查找输出文件..."
          find . -name "index.html" -type f | head -5
          find . -type d -name "*dist*" -o -name "*build*" | head -5

      - name: Copy Built Files
        run: |
          echo "正在复制构建文件..."

          # 查找构建输出
          echo "查找构建输出文件..."
          find ./feishin -name "index.html" -type f 2>/dev/null | head -10

          # 尝试多种可能的构建输出路径
          BUILD_PATH=""
          POSSIBLE_PATHS=(
            "./feishin/dist"
            "./feishin/build"
            "./feishin/dist/web"
            "./feishin/build/web"
            "./feishin/release/app/dist/web"
            "./feishin/out"
            "./feishin/public"
          )

          for path in "${POSSIBLE_PATHS[@]}"; do
            if [ -d "$path" ] && [ -f "$path/index.html" ]; then
              echo "✅ 找到构建路径: $path"
              BUILD_PATH="$path"
              break
            fi
          done

          # 如果还没找到，尝试查找任何包含 index.html 的目录
          if [ -z "$BUILD_PATH" ]; then
            INDEX_PATH=$(find ./feishin -name "index.html" -type f 2>/dev/null | head -1)
            if [ -n "$INDEX_PATH" ]; then
              BUILD_PATH=$(dirname "$INDEX_PATH")
              echo "✅ 通过 index.html 找到构建路径: $BUILD_PATH"
            fi
          fi

          if [ -z "$BUILD_PATH" ]; then
            echo "❌ 未找到有效的构建输出目录"
            echo "显示 feishin 目录结构:"
            find ./feishin -type d -maxdepth 3 2>/dev/null | sort
            echo "显示所有 HTML 文件:"
            find ./feishin -name "*.html" -type f 2>/dev/null
            exit 1
          fi

          # 备份旧版本（如果存在）
          if [ -d "./static" ]; then
            mv ./static ./static_backup_$(date +%Y%m%d_%H%M%S)
            echo "✅ 已备份旧版本"
          fi

          # 创建 static 目录
          mkdir -p ./static

          # 复制新构建的文件
          echo "从 $BUILD_PATH 复制文件到 ./static/"

          if [ -d "$BUILD_PATH" ]; then
            # 尝试复制目录内容
            if cp -r "$BUILD_PATH"/* ./static/ 2>/dev/null; then
              echo "✅ 复制目录内容成功"
            elif cp -r "$BUILD_PATH"/. ./static/ 2>/dev/null; then
              echo "✅ 复制隐藏文件成功"
            else
              echo "❌ 复制失败，尝试其他方法..."
              # 尝试使用 rsync
              if command -v rsync >/dev/null 2>&1; then
                rsync -av "$BUILD_PATH"/ ./static/
                echo "✅ 使用 rsync 复制成功"
              else
                echo "❌ 所有复制方法都失败了"
                exit 1
              fi
            fi
          else
            echo "❌ 构建路径不存在: $BUILD_PATH"
            exit 1
          fi

          echo "✅ 构建文件复制完成"

          # 验证复制结果
          if [ -f "./static/index.html" ]; then
            echo "✅ 验证成功: index.html 存在"
            echo "静态文件列表:"
            ls -la ./static/ | head -10
            echo "文件总数: $(find ./static -type f | wc -l)"

            # 显示 index.html 的前几行以确认内容
            echo "index.html 内容预览:"
            head -10 ./static/index.html
          else
            echo "❌ 验证失败: index.html 不存在"
            echo "static 目录内容:"
            ls -la ./static/ 2>/dev/null || echo "static 目录不存在"
            echo "BUILD_PATH 内容:"
            ls -la "$BUILD_PATH" 2>/dev/null || echo "BUILD_PATH 不存在"
            exit 1
          fi

          # 更新版本信息
          echo "${{ needs.check-updates.outputs.latest_version }}" > FEISHIN_VERSION

          # 生成更新日志
          echo "# 更新日志" > UPDATE_LOG.md
          echo "" >> UPDATE_LOG.md
          echo "## 版本: ${{ needs.check-updates.outputs.latest_version }}" >> UPDATE_LOG.md
          echo "更新时间: $(date '+%Y-%m-%d %H:%M:%S UTC')" >> UPDATE_LOG.md
          echo "构建触发: ${{ github.event_name }}" >> UPDATE_LOG.md
          echo "" >> UPDATE_LOG.md

      - name: Cleanup
        run: |
          echo "正在清理临时文件..."
          rm -rf feishin

      - name: Commit and Push Changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

          # 检查是否有变化
          if [ -n "$(git status --porcelain)" ]; then
            echo "发现文件变化，正在提交..."
            git add .
            git commit -m "🚀 自动更新到 Feishin ${{ needs.check-updates.outputs.latest_version }}

            - 从版本 ${{ needs.check-updates.outputs.current_version }} 更新到 ${{ needs.check-updates.outputs.latest_version }}
            - 构建时间: $(date '+%Y-%m-%d %H:%M:%S UTC')
            - 触发方式: ${{ github.event_name }}"

            git push
            echo "✅ 更新完成！"
          else
            echo "ℹ️ 没有文件变化"
          fi

  notify:
    name: Send Notification
    runs-on: ubuntu-latest
    needs: [check-updates, build]
    if: always()
    steps:
      - name: Create Issue Comment (if triggered by issue)
        if: github.event_name == 'issues' && needs.build.result == 'success'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 **构建完成！**

              ✅ 已成功更新到 Feishin ${{ needs.check-updates.outputs.latest_version }}

              📦 构建详情:
              - 原版本: ${{ needs.check-updates.outputs.current_version }}
              - 新版本: ${{ needs.check-updates.outputs.latest_version }}
              - 构建时间: ${new Date().toLocaleString('zh-CN', {timeZone: 'Asia/Shanghai'})}

              🌐 部署将在几分钟内生效。`
            })

      - name: Report Build Failure
        if: needs.build.result == 'failure'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            if (context.eventName === 'issues') {
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: `❌ **构建失败！**

                构建过程中遇到错误，请检查 [Actions 日志](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}) 获取详细信息。`
              })
            }