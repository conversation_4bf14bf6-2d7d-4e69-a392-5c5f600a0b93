name: Build Site

on:
  issues:
    types:
      - opened
  schedule:
    # 每天 UTC 时间 02:00 (北京时间 10:00) 检查更新
    - cron: '0 2 * * *'
  workflow_dispatch:
    # 允许手动触发
    inputs:
      force_build:
        description: '强制构建 (忽略版本检查)'
        required: false
        default: false
        type: boolean

permissions:
  contents: write
  issues: write

jobs:
  check-updates:
    name: Check for Feishin Updates
    runs-on: ubuntu-latest
    outputs:
      should_build: ${{ steps.version_check.outputs.should_build }}
      latest_version: ${{ steps.version_check.outputs.latest_version }}
      current_version: ${{ steps.version_check.outputs.current_version }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check Feishin Version
        id: version_check
        run: |
          # 获取 Feishin 最新版本
          LATEST_VERSION=$(curl -s https://api.github.com/repos/jeffvli/feishin/releases/latest | jq -r '.tag_name')
          echo "Latest Feishin version: $LATEST_VERSION"

          # 检查当前版本文件是否存在
          if [ -f "FEISHIN_VERSION" ]; then
            CURRENT_VERSION=$(cat FEISHIN_VERSION)
            echo "Current version: $CURRENT_VERSION"
          else
            CURRENT_VERSION="none"
            echo "No current version found"
          fi

          # 设置输出变量
          echo "latest_version=$LATEST_VERSION" >> $GITHUB_OUTPUT
          echo "current_version=$CURRENT_VERSION" >> $GITHUB_OUTPUT

          # 判断是否需要构建
          if [ "${{ github.event.inputs.force_build }}" = "true" ] || [ "$LATEST_VERSION" != "$CURRENT_VERSION" ] || [ "${{ github.event_name }}" = "issues" ]; then
            echo "should_build=true" >> $GITHUB_OUTPUT
            echo "需要构建新版本"
          else
            echo "should_build=false" >> $GITHUB_OUTPUT
            echo "版本无变化，跳过构建"
          fi

  build:
    name: Build and Deploy
    runs-on: ubuntu-latest
    needs: check-updates
    if: needs.check-updates.outputs.should_build == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.PAT }}

      - name: Checkout Feishin
        run: |
          echo "正在克隆 Feishin 仓库..."

          # 重试机制：最多尝试3次
          for i in {1..3}; do
            echo "尝试第 $i 次克隆..."
            if git clone --depth=1 https://github.com/jeffvli/feishin.git; then
              echo "✅ 克隆成功！"
              break
            else
              echo "❌ 克隆失败，等待 10 秒后重试..."
              sleep 10
              if [ $i -eq 3 ]; then
                echo "💥 克隆失败，已达到最大重试次数"
                exit 1
              fi
            fi
          done

          cd feishin
          echo "当前 Feishin 版本: $(git describe --tags --abbrev=0 2>/dev/null || echo 'unknown')"
          echo "仓库大小: $(du -sh . | cut -f1)"
          echo "package.json 存在: $(test -f package.json && echo 'yes' || echo 'no')"
          echo "package-lock.json 存在: $(test -f package-lock.json && echo 'yes' || echo 'no')"

      - name: Setup NodeJs
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install Dependencies
        working-directory: ./feishin
        run: |
          echo "正在安装依赖..."
          echo "Node.js 版本: $(node --version)"
          echo "npm 版本: $(npm --version)"

          # 清理可能的缓存问题
          npm cache clean --force

          # 尝试使用 npm ci，如果失败则使用 npm install
          if npm ci --legacy-peer-deps --ignore-scripts; then
            echo "✅ npm ci 成功"
          else
            echo "⚠️ npm ci 失败，尝试 npm install"
            npm install --legacy-peer-deps --ignore-scripts
          fi

      - name: Build Web Version
        working-directory: ./feishin
        run: |
          echo "正在构建 Web 版本..."
          echo "检查构建脚本..."
          npm run --silent | grep build || echo "可用脚本列表:"
          npm run

          echo "开始构建..."
          npm run build:web

      - name: Copy Built Files
        run: |
          echo "正在复制构建文件..."

          # 检查构建输出目录
          echo "检查构建输出..."
          find ./feishin -name "dist" -type d || echo "未找到 dist 目录"
          find ./feishin -name "web" -type d || echo "未找到 web 目录"

          # 检查预期的构建路径
          if [ -d "./feishin/release/app/dist/web" ]; then
            echo "✅ 找到标准构建路径: ./feishin/release/app/dist/web"
            BUILD_PATH="./feishin/release/app/dist/web"
          elif [ -d "./feishin/dist/web" ]; then
            echo "✅ 找到备用构建路径: ./feishin/dist/web"
            BUILD_PATH="./feishin/dist/web"
          elif [ -d "./feishin/build/web" ]; then
            echo "✅ 找到备用构建路径: ./feishin/build/web"
            BUILD_PATH="./feishin/build/web"
          else
            echo "❌ 未找到构建输出目录"
            echo "可用目录结构:"
            find ./feishin -type d -name "*web*" -o -name "*dist*" -o -name "*build*" | head -20
            exit 1
          fi

          # 备份旧版本（如果存在）
          if [ -d "./static" ]; then
            mv ./static ./static_backup_$(date +%Y%m%d_%H%M%S)
            echo "✅ 已备份旧版本"
          fi

          # 复制新构建的文件
          cp -afr "$BUILD_PATH" ./static
          echo "✅ 构建文件复制完成"

          # 验证复制结果
          if [ -f "./static/index.html" ]; then
            echo "✅ 验证成功: index.html 存在"
          else
            echo "❌ 验证失败: index.html 不存在"
            ls -la ./static/
            exit 1
          fi

          # 更新版本信息
          echo "${{ needs.check-updates.outputs.latest_version }}" > FEISHIN_VERSION

          # 生成更新日志
          echo "# 更新日志" > UPDATE_LOG.md
          echo "" >> UPDATE_LOG.md
          echo "## 版本: ${{ needs.check-updates.outputs.latest_version }}" >> UPDATE_LOG.md
          echo "更新时间: $(date '+%Y-%m-%d %H:%M:%S UTC')" >> UPDATE_LOG.md
          echo "构建触发: ${{ github.event_name }}" >> UPDATE_LOG.md
          echo "" >> UPDATE_LOG.md

      - name: Cleanup
        run: |
          echo "正在清理临时文件..."
          rm -rf feishin

      - name: Commit and Push Changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

          # 检查是否有变化
          if [ -n "$(git status --porcelain)" ]; then
            echo "发现文件变化，正在提交..."
            git add .
            git commit -m "🚀 自动更新到 Feishin ${{ needs.check-updates.outputs.latest_version }}

            - 从版本 ${{ needs.check-updates.outputs.current_version }} 更新到 ${{ needs.check-updates.outputs.latest_version }}
            - 构建时间: $(date '+%Y-%m-%d %H:%M:%S UTC')
            - 触发方式: ${{ github.event_name }}"

            git push
            echo "✅ 更新完成！"
          else
            echo "ℹ️ 没有文件变化"
          fi

  notify:
    name: Send Notification
    runs-on: ubuntu-latest
    needs: [check-updates, build]
    if: always()
    steps:
      - name: Create Issue Comment (if triggered by issue)
        if: github.event_name == 'issues' && needs.build.result == 'success'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 **构建完成！**

              ✅ 已成功更新到 Feishin ${{ needs.check-updates.outputs.latest_version }}

              📦 构建详情:
              - 原版本: ${{ needs.check-updates.outputs.current_version }}
              - 新版本: ${{ needs.check-updates.outputs.latest_version }}
              - 构建时间: ${new Date().toLocaleString('zh-CN', {timeZone: 'Asia/Shanghai'})}

              🌐 部署将在几分钟内生效。`
            })

      - name: Report Build Failure
        if: needs.build.result == 'failure'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            if (context.eventName === 'issues') {
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: `❌ **构建失败！**

                构建过程中遇到错误，请检查 [Actions 日志](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}) 获取详细信息。`
              })
            }