name: Build Site

on:
  issues:
    types:
      - opened
  schedule:
    # 每天 UTC 时间 02:00 (北京时间 10:00) 检查更新
    - cron: '0 2 * * *'
  workflow_dispatch:
    # 允许手动触发
    inputs:
      force_build:
        description: '强制构建 (忽略版本检查)'
        required: false
        default: false
        type: boolean

permissions:
  contents: write
  issues: write

jobs:
  check-updates:
    name: Check for Feishin Updates
    runs-on: ubuntu-latest
    outputs:
      should_build: ${{ steps.version_check.outputs.should_build }}
      latest_version: ${{ steps.version_check.outputs.latest_version }}
      current_version: ${{ steps.version_check.outputs.current_version }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check Feishin Version
        id: version_check
        run: |
          # 获取 Feishin 最新版本
          LATEST_VERSION=$(curl -s https://api.github.com/repos/jeffvli/feishin/releases/latest | jq -r '.tag_name')
          echo "Latest Feishin version: $LATEST_VERSION"

          # 检查当前版本文件是否存在
          if [ -f "FEISHIN_VERSION" ]; then
            CURRENT_VERSION=$(cat FEISHIN_VERSION)
            echo "Current version: $CURRENT_VERSION"
          else
            CURRENT_VERSION="none"
            echo "No current version found"
          fi

          # 设置输出变量
          echo "latest_version=$LATEST_VERSION" >> $GITHUB_OUTPUT
          echo "current_version=$CURRENT_VERSION" >> $GITHUB_OUTPUT

          # 判断是否需要构建
          if [ "${{ github.event.inputs.force_build }}" = "true" ] || [ "$LATEST_VERSION" != "$CURRENT_VERSION" ] || [ "${{ github.event_name }}" = "issues" ]; then
            echo "should_build=true" >> $GITHUB_OUTPUT
            echo "需要构建新版本"
          else
            echo "should_build=false" >> $GITHUB_OUTPUT
            echo "版本无变化，跳过构建"
          fi

  build:
    name: Build and Deploy
    runs-on: ubuntu-latest
    needs: check-updates
    if: needs.check-updates.outputs.should_build == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.PAT }}

      - name: Checkout Feishin
        run: |
          echo "正在克隆 Feishin 仓库..."

          # 重试机制：最多尝试3次
          for i in {1..3}; do
            echo "尝试第 $i 次克隆..."
            if git clone --depth=1 https://github.com/jeffvli/feishin.git; then
              echo "✅ 克隆成功！"
              break
            else
              echo "❌ 克隆失败，等待 10 秒后重试..."
              sleep 10
              if [ $i -eq 3 ]; then
                echo "💥 克隆失败，已达到最大重试次数"
                exit 1
              fi
            fi
          done

          cd feishin
          echo "当前 Feishin 版本: $(git describe --tags --abbrev=0 2>/dev/null || echo 'unknown')"
          echo "仓库大小: $(du -sh . | cut -f1)"
          echo "package.json 存在: $(test -f package.json && echo 'yes' || echo 'no')"
          echo "package-lock.json 存在: $(test -f package-lock.json && echo 'yes' || echo 'no')"

      - name: Setup NodeJs
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install Dependencies
        working-directory: ./feishin
        run: |
          echo "正在安装依赖..."
          echo "Node.js 版本: $(node --version)"
          echo "npm 版本: $(npm --version)"

          # 检查 package.json 中的脚本
          echo "检查可用的构建脚本:"
          cat package.json | jq '.scripts' || echo "无法解析 package.json"

          # 安装依赖，使用更宽松的配置
          npm install --legacy-peer-deps --force

          echo "✅ 依赖安装完成"

      - name: Build Web Version
        working-directory: ./feishin
        run: |
          echo "正在构建 Web 版本..."

          # 设置构建环境变量
          export NODE_OPTIONS="--max-old-space-size=4096"
          export CI=false

          # 检查是否存在 build:web 脚本
          BUILD_SUCCESS=false

          if npm run | grep -q "build:web"; then
            echo "使用 build:web 脚本"
            if npm run build:web; then
              BUILD_SUCCESS=true
            fi
          elif npm run | grep -q "build-web"; then
            echo "使用 build-web 脚本"
            if npm run build-web; then
              BUILD_SUCCESS=true
            fi
          elif npm run | grep -q "build"; then
            echo "使用通用 build 脚本"
            if npm run build; then
              BUILD_SUCCESS=true
            fi
          fi

          if [ "$BUILD_SUCCESS" = false ]; then
            echo "❌ 构建失败，尝试备用方案..."
            echo "可用脚本:"
            npm run

            # 尝试直接使用 vite build
            echo "尝试直接使用 vite build..."
            if npx vite build --mode production; then
              BUILD_SUCCESS=true
            else
              echo "vite build 也失败了"
            fi
          fi

          # 如果所有构建方法都失败，标记为失败但不退出
          if [ "$BUILD_SUCCESS" = false ]; then
            echo "❌ 所有构建方法都失败了"
            echo "BUILD_FAILED=true" >> $GITHUB_ENV
          else
            echo "✅ 构建成功"
            echo "BUILD_FAILED=false" >> $GITHUB_ENV
          fi

      - name: Download Pre-built Version (Fallback)
        if: ${{ env.BUILD_FAILED == 'true' }}
        run: |
          echo "⚠️ 构建失败，尝试下载预构建版本..."

          # 获取最新版本的下载链接
          LATEST_VERSION="${{ needs.check-updates.outputs.latest_version }}"
          echo "尝试下载版本: $LATEST_VERSION"

          # 尝试从 GitHub Releases 下载
          DOWNLOAD_URL="https://github.com/jeffvli/feishin/releases/download/$LATEST_VERSION/feishin-$LATEST_VERSION-web.zip"

          echo "下载链接: $DOWNLOAD_URL"

          if curl -L -f -o feishin-web.zip "$DOWNLOAD_URL"; then
            echo "✅ 下载成功"
            unzip -q feishin-web.zip -d ./feishin-web/
            echo "✅ 解压完成"

            # 查找解压后的文件
            find ./feishin-web -name "index.html" -type f

            echo "FALLBACK_DOWNLOAD=true" >> $GITHUB_ENV
          else
            echo "❌ 下载预构建版本失败"
            echo "FALLBACK_DOWNLOAD=false" >> $GITHUB_ENV
          fi

      - name: Copy Built Files
        run: |
          echo "正在复制构建文件..."

          BUILD_PATH=""

          # 如果使用了备用下载
          if [ "${FALLBACK_DOWNLOAD:-false}" = "true" ]; then
            echo "使用备用下载的文件..."

            # 查找下载的文件
            find ./feishin-web -name "index.html" -type f | head -5

            INDEX_PATH=$(find ./feishin-web -name "index.html" -type f | head -1)
            if [ -n "$INDEX_PATH" ]; then
              BUILD_PATH=$(dirname "$INDEX_PATH")
              echo "✅ 找到备用下载路径: $BUILD_PATH"
            fi
          else
            echo "使用构建的文件..."

            # 显示完整的目录结构以便调试
            echo "Feishin 目录结构:"
            find ./feishin -type d -maxdepth 3 | sort

            echo ""
            echo "查找可能的构建输出目录:"
            find ./feishin -type d \( -name "*dist*" -o -name "*build*" -o -name "*web*" -o -name "*out*" \) | sort

            # 查找包含 index.html 的目录
            echo ""
            echo "查找包含 index.html 的目录:"
            find ./feishin -name "index.html" -type f | head -10

            # 尝试多种可能的构建路径
            POSSIBLE_PATHS=(
              "./feishin/release/app/dist/web"
              "./feishin/dist/web"
              "./feishin/build/web"
              "./feishin/dist"
              "./feishin/build"
              "./feishin/out"
              "./feishin/public"
            )

            for path in "${POSSIBLE_PATHS[@]}"; do
              if [ -d "$path" ] && [ -f "$path/index.html" ]; then
                echo "✅ 找到构建路径: $path"
                BUILD_PATH="$path"
                break
              fi
            done

            # 如果还没找到，尝试查找任何包含 index.html 的目录
            if [ -z "$BUILD_PATH" ]; then
              INDEX_PATH=$(find ./feishin -name "index.html" -type f | head -1)
              if [ -n "$INDEX_PATH" ]; then
                BUILD_PATH=$(dirname "$INDEX_PATH")
                echo "✅ 通过 index.html 找到构建路径: $BUILD_PATH"
              fi
            fi
          fi

          if [ -z "$BUILD_PATH" ]; then
            echo "❌ 未找到有效的构建输出目录"
            echo "请检查构建是否成功完成"
            exit 1
          fi

          # 备份旧版本（如果存在）
          if [ -d "./static" ]; then
            mv ./static ./static_backup_$(date +%Y%m%d_%H%M%S)
            echo "✅ 已备份旧版本"
          fi

          # 创建 static 目录
          mkdir -p ./static

          # 复制新构建的文件
          if [ -d "$BUILD_PATH" ]; then
            cp -afr "$BUILD_PATH"/* ./static/ 2>/dev/null || cp -afr "$BUILD_PATH" ./static
          else
            echo "❌ 构建路径不存在: $BUILD_PATH"
            exit 1
          fi

          echo "✅ 构建文件复制完成"

          # 验证复制结果
          if [ -f "./static/index.html" ]; then
            echo "✅ 验证成功: index.html 存在"
            echo "静态文件列表:"
            ls -la ./static/ | head -10
          else
            echo "❌ 验证失败: index.html 不存在"
            echo "static 目录内容:"
            ls -la ./static/ 2>/dev/null || echo "static 目录不存在"
            exit 1
          fi

          # 更新版本信息
          echo "${{ needs.check-updates.outputs.latest_version }}" > FEISHIN_VERSION

          # 生成更新日志
          echo "# 更新日志" > UPDATE_LOG.md
          echo "" >> UPDATE_LOG.md
          echo "## 版本: ${{ needs.check-updates.outputs.latest_version }}" >> UPDATE_LOG.md
          echo "更新时间: $(date '+%Y-%m-%d %H:%M:%S UTC')" >> UPDATE_LOG.md
          echo "构建触发: ${{ github.event_name }}" >> UPDATE_LOG.md
          echo "" >> UPDATE_LOG.md

      - name: Cleanup
        run: |
          echo "正在清理临时文件..."
          rm -rf feishin

      - name: Commit and Push Changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

          # 检查是否有变化
          if [ -n "$(git status --porcelain)" ]; then
            echo "发现文件变化，正在提交..."
            git add .
            git commit -m "🚀 自动更新到 Feishin ${{ needs.check-updates.outputs.latest_version }}

            - 从版本 ${{ needs.check-updates.outputs.current_version }} 更新到 ${{ needs.check-updates.outputs.latest_version }}
            - 构建时间: $(date '+%Y-%m-%d %H:%M:%S UTC')
            - 触发方式: ${{ github.event_name }}"

            git push
            echo "✅ 更新完成！"
          else
            echo "ℹ️ 没有文件变化"
          fi

  notify:
    name: Send Notification
    runs-on: ubuntu-latest
    needs: [check-updates, build]
    if: always()
    steps:
      - name: Create Issue Comment (if triggered by issue)
        if: github.event_name == 'issues' && needs.build.result == 'success'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 **构建完成！**

              ✅ 已成功更新到 Feishin ${{ needs.check-updates.outputs.latest_version }}

              📦 构建详情:
              - 原版本: ${{ needs.check-updates.outputs.current_version }}
              - 新版本: ${{ needs.check-updates.outputs.latest_version }}
              - 构建时间: ${new Date().toLocaleString('zh-CN', {timeZone: 'Asia/Shanghai'})}

              🌐 部署将在几分钟内生效。`
            })

      - name: Report Build Failure
        if: needs.build.result == 'failure'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            if (context.eventName === 'issues') {
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: `❌ **构建失败！**

                构建过程中遇到错误，请检查 [Actions 日志](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}) 获取详细信息。`
              })
            }