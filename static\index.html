﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - 正在更新</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            text-align: center;
            max-width: 600px;
            background: rgba(255, 255, 255, 0.1);
            padding: 60px 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .logo {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            font-weight: 300;
        }
        
        .status {
            font-size: 1.2rem;
            margin-bottom: 30px;
            color: #add8e6;
        }
        
        .loading {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin: 20px 0;
        }
        
        .links {
            margin-top: 40px;
        }
        
        .links a {
            color: #add8e6;
            text-decoration: none;
            margin: 0 15px;
            padding: 10px 20px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 25px;
            transition: all 0.3s ease;
            display: inline-block;
        }
        
        .links a:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .info {
            margin-top: 30px;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 40px 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .logo {
                font-size: 3rem;
            }
            
            .links a {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🎵</div>
        <h1>Feishin</h1>
        <p class="status">正在更新到最新版本...</p>
        <div class="loading"></div>
        <p>我们正在部署最新版本的 Feishin 音乐播放器</p>
        
        <div class="links">
            <a href="https://github.com/jeffvli/feishin" target="_blank">官方仓库</a>
            <a href="https://github.com/jiayihello/Feishin-cf" target="_blank">部署仓库</a>
        </div>
        
        <div class="info">
            <p>如果长时间显示此页面，请检查 GitHub Actions 构建状态</p>
            <p>或联系管理员</p>
        </div>
    </div>
    
    <script>
        // 每30秒刷新一次页面，检查是否已更新
        setTimeout(() => {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>
