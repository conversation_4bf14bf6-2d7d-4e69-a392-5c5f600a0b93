{"feishin": {"repository": "jeffv<PERSON>/feishin", "build_command": "npm run build:web", "output_path": "release/app/dist/web", "target_path": "static"}, "monitoring": {"check_interval_hours": 1, "daily_build_time": "02:00", "timezone": "UTC"}, "notifications": {"create_issues": true, "auto_close_issues": true, "issue_labels": ["auto-update", "enhancement"]}, "build": {"node_version": "20", "npm_flags": "--legacy-peer-deps --ignore-scripts", "backup_old_builds": true, "generate_changelog": true}}