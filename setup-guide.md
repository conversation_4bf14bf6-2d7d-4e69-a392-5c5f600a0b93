# 🔧 Feishin-for-CF-Pages 设置指南

## 📋 前置要求

1. GitHub 账户
2. Cloudflare 账户
3. 基本的 Git 操作知识

## 🚀 快速设置

### 1. Fork 仓库

1. 点击本仓库右上角的 "Fork" 按钮
2. 选择你的 GitHub 账户作为目标

### 2. 配置 GitHub Secrets

为了让自动更新功能正常工作，需要配置以下 Secrets：

#### 创建 Personal Access Token (PAT)

1. 前往 GitHub Settings → Developer settings → Personal access tokens → Tokens (classic)
2. 点击 "Generate new token (classic)"
3. 设置以下权限：
   - `repo` (完整仓库访问权限)
   - `workflow` (工作流权限)
4. 复制生成的 token

#### 添加 Secrets 到仓库

1. 前往你 Fork 的仓库
2. 点击 Settings → Secrets and variables → Actions
3. 点击 "New repository secret"
4. 添加以下 Secret：
   - **Name**: `PAT`
   - **Value**: 刚才创建的 Personal Access Token

### 3. 配置 Cloudflare Pages

1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 前往 Pages 部分
3. 点击 "Create a project"
4. 选择 "Connect to Git"
5. 授权 GitHub 并选择你 Fork 的仓库
6. 配置构建设置：
   - **Project name**: 自定义名称
   - **Production branch**: `main` 或 `master`
   - **Build command**: 留空
   - **Build output directory**: `static`
7. 点击 "Save and Deploy"

### 4. 验证设置

1. 前往你的仓库的 Actions 页面
2. 手动运行 "Monitor Feishin Updates" 工作流
3. 检查是否成功创建了更新 Issue
4. 等待 "Build Site" 工作流自动运行
5. 确认 Cloudflare Pages 部署成功

## 🔄 自动更新工作原理

### 监控流程

```mermaid
graph TD
    A[每小时检查] --> B{有新版本?}
    B -->|是| C[创建 Issue]
    B -->|否| D[等待下次检查]
    C --> E[触发构建]
    E --> F[部署到 CF Pages]
    F --> G[关闭 Issue]
```

### 构建流程

1. **检测阶段**: 比较当前版本与 Feishin 最新版本
2. **构建阶段**: 克隆 Feishin 仓库并构建 Web 版本
3. **部署阶段**: 复制构建文件到 static 目录
4. **通知阶段**: 更新版本信息并发送通知

## 🛠️ 自定义配置

### 修改检查频率

编辑 `.github/workflows/monitor-feishin.yml` 中的 cron 表达式：

```yaml
schedule:
  # 每小时检查 (当前设置)
  - cron: '0 * * * *'
  
  # 每6小时检查
  # - cron: '0 */6 * * *'
  
  # 每天检查
  # - cron: '0 2 * * *'
```

### 修改构建配置

编辑 `.github/update-config.json` 来调整构建参数：

```json
{
  "build": {
    "node_version": "20",
    "npm_flags": "--legacy-peer-deps --ignore-scripts",
    "backup_old_builds": true,
    "generate_changelog": true
  }
}
```

## 🐛 故障排除

### 常见问题

1. **构建失败**
   - 检查 PAT 权限是否正确
   - 确认 Node.js 版本兼容性
   - 查看 Actions 日志获取详细错误信息

2. **自动更新不工作**
   - 验证 PAT 是否有效
   - 检查工作流是否被禁用
   - 确认仓库权限设置

3. **Cloudflare Pages 部署失败**
   - 检查构建输出目录设置
   - 确认文件权限正确
   - 查看 CF Pages 部署日志

### 调试步骤

1. 检查 Actions 页面的工作流运行状态
2. 查看最近的 Issues 是否有错误报告
3. 手动触发工作流测试功能
4. 检查 `FEISHIN_VERSION` 文件内容

## 📞 获取帮助

如果遇到问题：

1. 查看 [Issues](../../issues) 中是否有类似问题
2. 创建新的 Issue 并提供：
   - 详细的错误描述
   - Actions 日志截图
   - 你的配置信息（隐藏敏感信息）

## 🎉 完成设置

设置完成后，你的 Feishin-for-CF-Pages 将：

- ✅ 自动监控 Feishin 更新
- ✅ 自动构建和部署新版本
- ✅ 通过 Issues 提供状态通知
- ✅ 维护版本历史记录

享受自动化的 Feishin 部署体验！🚀
