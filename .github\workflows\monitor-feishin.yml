name: Monitor Feishin Updates

on:
  schedule:
    # 每小时检查一次 Feishin 是否有新发布
    - cron: '0 * * * *'
  workflow_dispatch:
    # 允许手动触发监控

permissions:
  contents: read
  issues: write

jobs:
  monitor:
    name: Monitor Feishin Releases
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.PAT }}

      - name: Check for New Feishin Release
        id: check_release
        run: |
          # 获取 Feishin 最新发布信息
          RELEASE_INFO=$(curl -s https://api.github.com/repos/jeffvli/feishin/releases/latest)
          LATEST_VERSION=$(echo "$RELEASE_INFO" | jq -r '.tag_name')
          RELEASE_DATE=$(echo "$RELEASE_INFO" | jq -r '.published_at')
          RELEASE_BODY=$(echo "$RELEASE_INFO" | jq -r '.body')
          
          echo "Latest version: $LATEST_VERSION"
          echo "Release date: $RELEASE_DATE"
          
          # 检查当前版本
          if [ -f "FEISHIN_VERSION" ]; then
            CURRENT_VERSION=$(cat FEISHIN_VERSION)
          else
            CURRENT_VERSION="none"
          fi
          
          echo "Current version: $CURRENT_VERSION"
          
          # 设置输出变量
          echo "latest_version=$LATEST_VERSION" >> $GITHUB_OUTPUT
          echo "current_version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
          echo "release_date=$RELEASE_DATE" >> $GITHUB_OUTPUT
          
          # 简化处理，不传递复杂的发布说明
          echo "release_body=检测到新版本发布" >> $GITHUB_OUTPUT
          
          # 判断是否有新版本
          if [ "$LATEST_VERSION" != "$CURRENT_VERSION" ]; then
            echo "has_update=true" >> $GITHUB_OUTPUT
            echo "🆕 发现新版本: $LATEST_VERSION"
          else
            echo "has_update=false" >> $GITHUB_OUTPUT
            echo "ℹ️ 当前已是最新版本"
          fi

      - name: Create Issue for New Release
        if: steps.check_release.outputs.has_update == 'true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.PAT }}
          script: |
            // 检查是否已存在相同版本的 issue
            const existingIssues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              labels: 'auto-update'
            });

            const latest_version = '${{ steps.check_release.outputs.latest_version }}';
            const versionIssueExists = existingIssues.data.some(issue =>
              issue.title.includes(latest_version)
            );

            if (!versionIssueExists) {
              const current_version = '${{ steps.check_release.outputs.current_version }}';
              const release_date = '${{ steps.check_release.outputs.release_date }}';

              const issue = await github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: '🔄 自动更新到 Feishin ' + latest_version,
                body: '🚀 **检测到 Feishin 新版本发布！**\n\n## 版本信息\n- **新版本**: ' + latest_version + '\n- **当前版本**: ' + current_version + '\n- **发布时间**: ' + release_date + '\n\n---\n\n**自动构建将在此 Issue 创建后开始。**\n\n> 此 Issue 由自动监控系统创建。构建完成后将自动关闭。',
                labels: ['auto-update', 'enhancement']
              });

              console.log('✅ 已创建 Issue #' + issue.data.number + ' 触发自动构建');
            } else {
              console.log('ℹ️ 该版本的更新 Issue 已存在，跳过创建');
            }

      - name: Close Completed Update Issues
        if: steps.check_release.outputs.has_update == 'false'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.PAT }}
          script: |
            // 关闭已完成的自动更新 Issues
            const issues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              labels: 'auto-update'
            });
            
            const currentVersion = '${{ steps.check_release.outputs.current_version }}';
            
            for (const issue of issues.data) {
              if (issue.title.includes(currentVersion)) {
                await github.rest.issues.createComment({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: issue.number,
                  body: '✅ **更新完成！**\n\n已成功更新到版本 ' + currentVersion + '。\n\n🌐 新版本现已部署到 Cloudflare Pages。'
                });
                
                await github.rest.issues.update({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: issue.number,
                  state: 'closed'
                });
                
                console.log(`✅ 已关闭完成的更新 Issue #${issue.number}`);
              }
            }
