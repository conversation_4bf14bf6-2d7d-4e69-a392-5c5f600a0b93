# Feishin-for-CF-Pages

本项目提供了将 [Feishin](https://github.com/jeffvli/feishin) 部署在 Cloudflare Pages 上的一种方法，并支持自动检测和更新到最新版本。

## 🚀 功能特性

- ✅ **自动更新检测**: 每小时自动检查 Feishin 是否有新版本发布
- 🔄 **自动构建部署**: 检测到新版本时自动触发构建和部署
- 📝 **版本跟踪**: 自动记录和管理版本信息
- 🤖 **智能通知**: 通过 GitHub Issues 提供更新状态通知
- 🕒 **定时检查**: 每天定时检查确保不遗漏任何更新
- 🛠️ **手动触发**: 支持手动触发构建和强制更新

## 📋 更新机制

### 自动更新流程

1. **监控检测** (`monitor-feishin.yml`):
   - 每小时检查 Feishin 仓库的最新发布
   - 对比当前版本与最新版本
   - 发现新版本时自动创建 Issue

2. **构建部署** (`build.yml`):
   - Issue 创建时自动触发构建流程
   - 克隆最新的 Feishin 代码
   - 构建 Web 版本并部署到静态文件目录
   - 更新版本记录和生成更新日志

3. **状态管理**:
   - 构建完成后自动关闭相关 Issue
   - 提供详细的构建状态和错误信息
   - 维护版本历史记录

### 触发方式

- 🔄 **自动触发**: 检测到新版本时自动开始
- 📝 **Issue 触发**: 手动创建 Issue 立即触发构建
- ⏰ **定时触发**: 每天定时检查并构建（如有更新）
- 🛠️ **手动触发**: 在 Actions 页面手动运行工作流

## 📊 版本信息

当前 Feishin 版本: ![Version](https://img.shields.io/badge/dynamic/json?color=blue&label=Feishin&query=tag_name&url=https%3A%2F%2Fapi.github.com%2Frepos%2Fjeffvli%2Ffeishin%2Freleases%2Flatest)

## 🔧 配置说明

### 必需的 Secrets

确保在仓库设置中配置以下 Secrets：

- `PAT`: Personal Access Token，用于自动提交和创建 Issues

### 工作流文件

- `.github/workflows/build.yml`: 主要的构建和部署工作流
- `.github/workflows/monitor-feishin.yml`: Feishin 版本监控工作流

## 📖 使用说明

### 部署到 Cloudflare Pages

1. Fork 本仓库
2. 在 Cloudflare Pages 中连接你的仓库
3. 设置构建配置：
   - 构建命令: 留空（使用预构建文件）
   - 构建输出目录: `static`
4. 配置必要的 Secrets
5. 等待自动检测和构建完成

### 手动触发更新

如果需要立即检查和更新：

1. 前往仓库的 Actions 页面
2. 选择 "Build Site" 工作流
3. 点击 "Run workflow" 并选择是否强制构建

或者创建一个新的 Issue，系统会自动开始构建流程。

## 📚 详细教程

详细部署教程: https://blog.highp.ing/p/feishin-for-cfpages/

## 🙏 致谢

- [Feishin](https://github.com/jeffvli/feishin) - 优秀的音乐播放器项目
- [Cloudflare Pages](https://pages.cloudflare.com/) - 免费的静态网站托管服务
- GitHub Actions - 强大的 CI/CD 平台

## 📄 许可证

本项目遵循 MIT 许可证。Feishin 项目请参考其原始许可证。

